import { z } from 'zod';

// Core Agent Types
export interface AgentConfig {
  provider: 'deepseek' | 'ollama';
  model: string;
  temperature?: number;
  maxTokens?: number;
  apiKey?: string;
  baseUrl?: string;
}

export interface AgentMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  toolCalls?: ToolCall[];
  toolCallId?: string;
}

export interface ToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

export interface ToolResult {
  toolCallId: string;
  result: ToolExecutionResult;
  error?: string;
}

export interface ToolExecutionResult {
  success: boolean;
  data?: unknown;
  error?: string;
  message?: string;
  metadata?: Record<string, unknown>;
}

// Tool System Types
export interface Tool {
  name: string;
  description: string;
  parameters: z.ZodSchema;
  execute: (params: unknown, context: ExecutionContext) => Promise<ToolExecutionResult>;
}

export interface ExecutionContext {
  sessionId: string;
  workingDirectory: string;
  environment: Record<string, string>;
  projectContext: ProjectContext;
  agent: AgentInstance;
}

export interface AgentInstance {
  id: string;
  config: AgentConfig;
  sendMessage: (message: string) => Promise<string>;
  executeTools: (toolCalls: ToolCall[]) => Promise<ToolResult[]>;
}

// Session Management Types
export interface Session {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  workingDirectory: string;
  context: SessionContext;
  messages: AgentMessage[];
  metadata: Record<string, unknown>;
}

export interface SessionContext {
  projectStructure: ProjectStructure;
  fileIndex: FileIndex;
  dependencies: DependencyInfo[];
  environment: Record<string, string>;
  gitInfo?: GitInfo | undefined;
}

export interface SessionStats {
  session: {
    id: string;
    name: string;
    age: number;
    lastActivity: number;
    workingDirectory: string;
  };
  messages: {
    total: number;
    byRole: Record<string, number>;
    withToolCalls: number;
  };
  context: {
    files: number;
    directories: number;
    dependencies: number;
    lastIndexed: Date;
  };
  metadata: Record<string, unknown>;
}

// Project Context Types
export interface ProjectContext {
  root: string;
  type: ProjectType;
  structure: ProjectStructure;
  dependencies: DependencyInfo[];
  configuration: ProjectConfiguration;
  gitInfo?: GitInfo | undefined;
}

export interface ProjectStructure {
  directories: DirectoryNode[];
  files: FileNode[];
  totalFiles: number;
  totalDirectories: number;
  lastIndexed: Date;
}

export interface DirectoryNode {
  path: string;
  name: string;
  children: (DirectoryNode | FileNode)[];
  permissions: FilePermissions;
}

export interface FileNode {
  path: string;
  name: string;
  size: number;
  extension: string;
  mimeType: string;
  lastModified: Date;
  permissions: FilePermissions;
  content?: string;
  summary?: string;
}

export interface FilePermissions {
  readable: boolean;
  writable: boolean;
  executable: boolean;
  mode: string;
}

export interface FileIndex {
  files: Map<string, FileNode>;
  directories: Map<string, DirectoryNode>;
  byExtension: Map<string, FileNode[]>;
  bySize: Map<string, FileNode[]>;
  searchIndex: Map<string, FileNode[]>;
}

// Project Types
export type ProjectType = 
  | 'nodejs' 
  | 'python' 
  | 'rust' 
  | 'go' 
  | 'java' 
  | 'csharp' 
  | 'cpp' 
  | 'web' 
  | 'mobile' 
  | 'unknown';

export interface DependencyInfo {
  name: string;
  version: string;
  type: 'production' | 'development' | 'peer' | 'optional';
  source: string;
  description?: string;
}

export interface ProjectConfiguration {
  packageManager?: string;
  buildTool?: string;
  testFramework?: string;
  linter?: string;
  formatter?: string;
  bundler?: string;
  framework?: string;
  language?: string;
  version?: string;
}

export interface GitInfo {
  branch: string;
  commit: string;
  remote?: string;
  status: GitStatus;
  isRepo: boolean;
}

export interface GitStatus {
  staged: string[];
  unstaged: string[];
  untracked: string[];
  ahead: number;
  behind: number;
}

// LLM Provider Types
export interface LLMProvider {
  name: string;
  sendMessage: (messages: AgentMessage[], config: AgentConfig) => Promise<string>;
  sendToolMessage: (messages: AgentMessage[], tools: Tool[], config: AgentConfig) => Promise<{
    message: string;
    toolCalls?: ToolCall[];
  }>;
  validateConfig: (config: AgentConfig) => boolean;
}

// Configuration Types
export interface CLIConfig {
  defaultProvider: 'deepseek' | 'ollama';
  providers: {
    deepseek: {
      apiKey?: string | undefined;
      baseUrl: string;
      defaultModel: string;
    };
    ollama: {
      baseUrl: string;
      defaultModel: string;
    };
  };
  session: {
    autoSave: boolean;
    maxHistory: number;
    persistContext: boolean;
  };
  context: {
    autoIndex: boolean;
    watchFiles: boolean;
    maxFileSize: number;
    excludePatterns: string[];
  };
  tools: {
    allowShellExecution: boolean;
    allowFileOperations: boolean;
    allowNetworkAccess: boolean;
    restrictedPaths: string[];
    maxConcurrentExecutions: number;
    timeoutMs: number;
    allowDangerousCommands: boolean;
    allowedCommands: string[];
    blockedCommands: string[];
    enableRealTimeOutput: boolean;
    maxParallelTools: number;
  };
}

// Command Types
export interface CommandOptions {
  provider?: 'deepseek' | 'ollama';
  model?: string;
  session?: string;
  verbose?: boolean;
  debug?: boolean;
  temperature?: number;
  maxTokens?: number;
  cursorMode?: boolean;
  promptTemplate?: string;
}

// Error Types
export class AgentError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: unknown
  ) {
    super(message);
    this.name = 'AgentError';
  }
}

export class ToolError extends Error {
  constructor(
    message: string,
    public toolName: string,
    public details?: unknown
  ) {
    super(message);
    this.name = 'ToolError';
  }
}

export class SessionError extends Error {
  constructor(
    message: string,
    public sessionId?: string,
    public details?: unknown
  ) {
    super(message);
    this.name = 'SessionError';
  }
}

// Prompt System Types
export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  content: string;
  variables: PromptVariable[];
  category: PromptCategory;
  version: string;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, unknown>;
}

export interface PromptVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description: string;
  required: boolean;
  defaultValue?: unknown;
  validation?: z.ZodSchema;
}

export type PromptCategory =
  | 'system'
  | 'coding'
  | 'debugging'
  | 'analysis'
  | 'documentation'
  | 'testing'
  | 'refactoring'
  | 'custom';

export interface PromptContext {
  userInfo?: {
    os: string;
    shell: string;
    workspace: string;
  };
  projectInfo?: {
    type: ProjectType;
    structure: ProjectStructure;
    dependencies: DependencyInfo[];
    configuration: ProjectConfiguration;
  };
  sessionInfo?: {
    id: string;
    messageCount: number;
    toolCallCount: number;
    uptime: number;
  };
  fileContext?: {
    openFiles: string[];
    recentFiles: string[];
    currentFile?: string;
    cursorPosition?: { line: number; column: number };
  };
  variables: Record<string, unknown>;
}

export interface PromptSystemConfig {
  enabled: boolean;
  defaultTemplate: string;
  autoInjectContext: boolean;
  maxContextSize: number;
  templateDirectory: string;
  customTemplates: string[];
  behaviorRules: {
    communication: BehaviorRule[];
    toolCalling: BehaviorRule[];
    codeChanges: BehaviorRule[];
    debugging: BehaviorRule[];
    apiUsage: BehaviorRule[];
  };
}

export interface BehaviorRule {
  id: string;
  description: string;
  rule: string;
  priority: number;
  enabled: boolean;
  category: string;
}

export interface PromptExecution {
  templateId: string;
  context: PromptContext;
  variables: Record<string, unknown>;
  renderedPrompt: string;
  timestamp: Date;
  success: boolean;
  error?: string;
  metadata: Record<string, unknown>;
}

// Enhanced Tool Types for Cursor Compatibility
export interface CursorToolSchema {
  type: 'function';
  function: {
    name: string;
    description: string;
    parameters: {
      type: 'object';
      properties: Record<string, {
        type: string;
        description: string;
        enum?: string[];
        items?: { type: string };
        default?: unknown;
      }>;
      required: string[];
    };
  };
}

export interface CursorToolCall {
  toolName: string;
  parameters: Record<string, unknown>;
  explanation: string;
}

export interface CursorToolResult {
  toolName: string;
  success: boolean;
  result: unknown;
  explanation: string;
  error?: string;
  metadata?: Record<string, unknown>;
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
