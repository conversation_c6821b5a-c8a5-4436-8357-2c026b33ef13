import { readFileSync, existsSync, readdirSync } from 'fs';
import { join, extname } from 'path';
import { z } from 'zod';
import { PromptTemplate, AgentMessage, Tool } from '@/types';
import { logger } from '@/utils/logger';

// Zod schema for prompt template validation
const PromptTemplateSchema = z.object({
  name: z.string(),
  description: z.string(),
  version: z.string(),
  model: z.string().optional(),
  temperature: z.number().min(0).max(2).optional(),
  messages: z.array(z.object({
    role: z.enum(['system', 'user', 'assistant', 'tool']),
    content: z.string(),
    toolCallId: z.string().optional(),
    toolCalls: z.array(z.any()).optional(),
  })),
  tools: z.array(z.object({
    type: z.literal('function'),
    function: z.object({
      name: z.string(),
      description: z.string(),
      parameters: z.record(z.any()),
    }),
  })).optional(),
  tool_choice: z.string().optional(),
  stream: z.boolean().optional(),
  variables: z.record(z.string()).optional(),
  metadata: z.record(z.any()).optional(),
});

export class PromptManager {
  private templates: Map<string, PromptTemplate> = new Map();
  private templatesDirectory: string;

  constructor(templatesDirectory?: string) {
    // Default to the prompts directory relative to the project root
    this.templatesDirectory = templatesDirectory ?? join(process.cwd(), 'src', 'prompts');
    this.loadTemplates();
  }

  /**
   * Load all prompt templates from the templates directory
   */
  private loadTemplates(): void {
    try {
      if (!existsSync(this.templatesDirectory)) {
        logger.warn(`Prompts directory not found: ${this.templatesDirectory}`, {}, 'PromptManager');
        return;
      }

      const files = readdirSync(this.templatesDirectory);
      const jsonFiles = files.filter(file => extname(file) === '.json');

      for (const file of jsonFiles) {
        try {
          const filePath = join(this.templatesDirectory, file);
          const content = readFileSync(filePath, 'utf-8');
          const templateData = JSON.parse(content);
          
          // Validate the template
          const validatedTemplate = PromptTemplateSchema.parse(templateData);
          
          this.templates.set(validatedTemplate.name, validatedTemplate);
          
          logger.debug(`Loaded prompt template: ${validatedTemplate.name}`, {
            file,
            version: validatedTemplate.version
          }, 'PromptManager');
        } catch (error) {
          logger.error(`Failed to load prompt template from ${file}`, error, 'PromptManager');
        }
      }

      logger.info(`Loaded ${this.templates.size} prompt templates`, {
        templates: Array.from(this.templates.keys())
      }, 'PromptManager');
    } catch (error) {
      logger.error('Failed to load prompt templates', error, 'PromptManager');
    }
  }

  /**
   * Get a prompt template by name
   */
  public getTemplate(name: string): PromptTemplate | null {
    const template = this.templates.get(name);
    if (!template) {
      logger.warn(`Prompt template not found: ${name}`, {
        availableTemplates: Array.from(this.templates.keys())
      }, 'PromptManager');
      return null;
    }
    return template;
  }

  /**
   * Get all available template names
   */
  public getAvailableTemplates(): string[] {
    return Array.from(this.templates.keys());
  }

  /**
   * Process a template with variable substitution
   */
  public processTemplate(
    templateName: string,
    variables?: Record<string, string>,
    tools?: Tool[]
  ): { messages: AgentMessage[]; tools: Tool[] | undefined; config?: Partial<PromptTemplate> } | null {
    const template = this.getTemplate(templateName);
    if (!template) {
      return null;
    }

    try {
      // Process messages with variable substitution
      const processedMessages = template.messages.map(message => ({
        ...message,
        content: this.substituteVariables(message.content, variables || template.variables || {})
      }));

      // Use tools from template if provided, otherwise use passed tools
      const finalTools = template.tools ? this.convertPromptToolsToTools(template.tools) : tools;

      // Extract configuration
      const config: Partial<PromptTemplate> = {
        ...(template.model && { model: template.model }),
        ...(template.temperature !== undefined && { temperature: template.temperature }),
        ...(template.tool_choice && { tool_choice: template.tool_choice }),
        ...(template.stream !== undefined && { stream: template.stream }),
      };

      logger.debug(`Processed prompt template: ${templateName}`, {
        messagesCount: processedMessages.length,
        toolsCount: finalTools?.length || 0,
        hasVariables: Object.keys(variables || {}).length > 0
      }, 'PromptManager');

      return {
        messages: processedMessages,
        tools: finalTools,
        config
      };
    } catch (error) {
      logger.error(`Failed to process prompt template: ${templateName}`, error, 'PromptManager');
      return null;
    }
  }

  /**
   * Substitute variables in content
   */
  private substituteVariables(content: string, variables: Record<string, string>): string {
    let result = content;
    
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      result = result.replace(regex, value);
    }
    
    return result;
  }

  /**
   * Convert prompt tools to Tool interface
   */
  private convertPromptToolsToTools(promptTools: any[]): Tool[] {
    return promptTools.map(promptTool => ({
      name: promptTool.function.name,
      description: promptTool.function.description,
      parameters: promptTool.function.parameters as any, // This would need proper Zod schema conversion
      execute: async () => {
        throw new Error(`Tool ${promptTool.function.name} from prompt template is not executable`);
      }
    }));
  }

  /**
   * Reload templates from disk
   */
  public reloadTemplates(): void {
    this.templates.clear();
    this.loadTemplates();
  }

  /**
   * Check if a template exists
   */
  public hasTemplate(name: string): boolean {
    return this.templates.has(name);
  }

  /**
   * Get template metadata
   */
  public getTemplateInfo(name: string): Pick<PromptTemplate, 'name' | 'description' | 'version'> | null {
    const template = this.getTemplate(name);
    if (!template) {
      return null;
    }
    
    return {
      name: template.name,
      description: template.description,
      version: template.version
    };
  }
}

// Export singleton instance
export const promptManager = new PromptManager();
