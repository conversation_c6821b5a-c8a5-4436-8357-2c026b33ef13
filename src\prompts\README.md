# Prompt System

This directory contains the prompt templates and management system for the Agentic CLI tool.

## Overview

The prompt system allows you to define reusable prompt templates that can be dynamically loaded and applied to agent conversations. This enables consistent behavior and easy customization of the AI assistant's personality and capabilities.

## Features

- **Template Management**: Load and manage multiple prompt templates
- **Variable Substitution**: Dynamic variable replacement in templates
- **Tool Integration**: Templates can define their own tool configurations
- **Validation**: Zod schema validation for template structure
- **Hot Reloading**: Templates can be reloaded without restarting the application

## Template Structure

Each prompt template is a JSON file with the following structure:

```json
{
  "name": "template-name",
  "description": "Template description",
  "version": "1.0.0",
  "model": "optional-model-name",
  "temperature": 0.7,
  "messages": [
    {
      "role": "system",
      "content": "System prompt content with {{variables}}"
    },
    {
      "role": "user", 
      "content": "Optional user message"
    }
  ],
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "tool_name",
        "description": "Tool description",
        "parameters": {
          "type": "object",
          "properties": {
            "param": {
              "type": "string",
              "description": "Parameter description"
            }
          },
          "required": ["param"]
        }
      }
    }
  ],
  "tool_choice": "auto",
  "stream": true,
  "variables": {
    "defaultVar": "defaultValue"
  },
  "metadata": {
    "author": "Template author",
    "category": "system"
  }
}
```

## Available Templates

### agentic-assistant
The default template for the Agentic CLI assistant. Provides:
- Professional coding assistance personality
- Clear communication guidelines
- Tool calling instructions
- Code change best practices
- Debugging guidelines
- API usage instructions

## Usage

### CLI Commands

```bash
# Use specific template
agentic chat --prompt-template agentic-assistant

# List available templates
/templates

# Set template during chat
/set-template template-name

# Show current template
/template
```

### Programmatic Usage

```typescript
import { promptManager } from '@/prompts/manager';

// Get available templates
const templates = promptManager.getAvailableTemplates();

// Load a template
const template = promptManager.getTemplate('agentic-assistant');

// Process template with variables
const processed = promptManager.processTemplate('agentic-assistant', {
  workingDirectory: '/path/to/project',
  projectType: 'nodejs',
  sessionId: 'session-123'
});
```

## Configuration

The prompt system is configured in the main CLI configuration:

```json
{
  "prompts": {
    "enabled": true,
    "defaultTemplate": "agentic-assistant",
    "templatesDirectory": "src/prompts",
    "enableVariableSubstitution": true,
    "customTemplates": {},
    "autoInjectContext": true,
    "maxContextSize": 32000,
    "useCursorMode": false,
    "enableBehaviorRules": true,
    "customTemplatesEnabled": true
  }
}
```

## Variable Substitution

Templates support variable substitution using `{{variableName}}` syntax:

- `{{workingDirectory}}` - Current working directory
- `{{projectType}}` - Detected project type
- `{{sessionId}}` - Current session ID
- Custom variables can be passed when processing templates

## Creating Custom Templates

1. Create a new JSON file in the `src/prompts/` directory
2. Follow the template structure above
3. Use descriptive names and include version information
4. Test the template using the CLI commands
5. The template will be automatically loaded on next startup

## Best Practices

- Keep system messages focused and clear
- Use variable substitution for dynamic content
- Include comprehensive tool descriptions
- Version your templates for tracking changes
- Test templates thoroughly before deployment
- Document any custom variables or requirements

## Troubleshooting

- **Template not loading**: Check JSON syntax and file location
- **Variables not substituting**: Ensure correct `{{variable}}` syntax
- **Tools not working**: Verify tool schemas match actual tool implementations
- **Performance issues**: Consider template size and complexity

## Development

The prompt system consists of:

- `manager.ts` - Core prompt management functionality
- `README.md` - This documentation
- `*.json` - Template files

To extend the system:
1. Add new features to the PromptManager class
2. Update type definitions in `src/types/index.ts`
3. Add corresponding CLI commands in `src/cli.ts`
4. Update documentation and examples
